import 'assets/css/style.scss'
import Canvas from './Canvas'
import { useReactFlow, useNodes } from '@xyflow/react'
import {
  Tooltip,
  message as Message,
  Input,
  Button,
  Popover,
  Modal,
  Form,
  Spin,
  Space,
  message,
  Dropdown
} from 'antd'
import {
  SaveOutlined,
  WarningOutlined,
  FormOutlined,
  CheckOutlined,
  ArrowLeftOutlined,
  LeftOutlined,
  LoadingOutlined,
  DownOutlined
} from '@ant-design/icons'
import IconDebug from 'assets/svgs/debug.svg?react'
import IconPublish from 'assets/svgs/publish.svg?react'

import { useCallback, useEffect, useRef, useState } from 'react'
import { transformFlowNodeData } from '@/utils/dataTransform'
import { useNavigate, useParams } from 'react-router-dom'
import { useFullscreen, useRequest, useWhyDidYouUpdate } from 'ahooks'
// import usePrompt from 'utils/usePrompt'
import useStore from '@/store'
import { useModelStore } from '@/store/model'

import ajax from '@/utils/http'

import dayjs from 'dayjs'

import styles from './Canvas/style.module.scss'
import eventBus from '@/utils/eventBus'
import useInIframe from '@/hooks/useInIframe'
import useClickOutside from '@/hooks/useClickOutside'
import { validateTrim } from '@/utils/validateTrim'
import { APP_ENV } from '@/utils/constant'
import PublishApiModal from './PublishApiModal'

function Workflow({ workflowId }) {
  const navigate = useNavigate()
  const [form] = Form.useForm()
  const reactFlowInstance = useReactFlow()
  const editorWrap = useRef(null)
  const [isFullscreen, { toggleFullscreen }] = useFullscreen(editorWrap)
  const { isDirty, setIsDirty } = useStore()
  const [workflowData, setWorkflowData] = useState(null)

  const workflowDataRef = useRef(null)

  const [isModalOpen, setIsModalOpen] = useState(false)

  const [debugNumber, setDebugNumber] = useState(0)

  const maasModels = useModelStore((state) => state.maasModel)


  const [experienceVisible, setExperienceVisible] = useState(false)

  const inIframe = useInIframe()

  const [editLoading, setEditLoading] = useState(false)
  const [publishLoading, setPublishLoading] = useState(false)

  const [publishApiModalOpen, setPublishApiModalOpen] = useState(false)

  const [debugLoading, setDebugLoading] = useState(false)

  const [publishDebugLoading, setPublishDebugLoading] = useState(false)

  const [publishAPIDebugLoading, setPublishAPIDebugLoading] = useState(false)
  const [publishAPILoading, setPublishAPILoading] = useState(false)

  const [dropDownOpen, setDropDownOpen] = useState(false)

  const dropdownRef = useClickOutside(() => setDropDownOpen(false))

  const onPublishEnter = (e) => {
    e.preventDefault()
    setDropDownOpen(true)
  }

  useEffect(() => {
    if (isDirty) {
      saveData()
    }
  }, [isDirty])

  useEffect(() => {
    window.addEventListener('message', handleMessageFromParent)

    return () => {
      window.removeEventListener('message', handleMessageFromParent)
    }
  }, [])

  useEffect(() => {
    let param = {
      id: workflowId
    }
    if (inIframe) {
      param.publish = true
    }
    ajax({
      url: `/workflow/get`,
      method: 'get',
      data: param
    })
      .then((res) => {
        console.log('从接口获取的schema', JSON.parse(res?.data?.data?.schema || '{}'))
        if (res.data.code === '0') {
          // 给汽车发送一份数据
          if (APP_ENV === 'auto') {
            const { schema, ...rest } = res?.data?.data || {}
            window.microApp?.dispatch({ type: 'WORKFLOW_DATA', data: rest })
          }

          setWorkflowData(res?.data?.data)
          workflowDataRef.current = res?.data?.data
        }
      })
      .catch((res) => {
        if (res.data.code === '151004') {
          if (!inIframe) {
            navigate('/')
          }
        }
      })
  }, [inIframe])

  const saveBasicInfo = async (values) => {
    setEditLoading(true)
    try {
      const result = await ajax({
        url: '/workflow/save',
        method: 'post',
        data: JSON.stringify({
          name: values.name,
          id: workflowId,
          description: values.description
        })
      })
      console.log('保存结果', result)
      if (result.data.code === '0') {
        Message.success('修改成功')
        setIsModalOpen(false)
        setWorkflowData((data) => {
          return {
            ...data,
            name: values.name,
            description: values.description
          }
        })
      }
    } catch (e) {
    } finally {
      setEditLoading(false)
    }
  }

  const saveData = async () => {
    const flow = reactFlowInstance.toObject()

    console.log(
      '---------------------------------------------------------flow-----',
      flow.nodes,
      flow.edges
    )

    const obj = transformFlowNodeData(flow.nodes, flow.edges)

    console.log('待序列化的obj', obj)

    try {
      const result = await ajax({
        url: '/workflow/save',
        method: 'post',
        data: JSON.stringify({
          // name,
          id: workflowId,
          schema: JSON.stringify(obj)
        })
      })
      console.log('保存结果', result)
      if (result.data.code === '0') {
        console.log('保存成功！')
        setTimestamp(dayjs(new Date().getTime()).format('HH:mm:ss'))
      } else {
      }
    } catch (e) {
    } finally {
      setTimeout(() => {
        setIsDirty(false)
      })
    }
  }

  const onEditClick = useCallback(() => {
    setIsModalOpen(true)
  }, [])

  const doPublish = async () => {
    setPublishLoading(true)
    try {
      const result = await ajax({
        url: '/workflow/publish',
        method: 'get',
        data: {
          id: workflowId,
          flowId: workflowData.flowId
        }
      })
      console.log('保存结果', result)
      if (result.data.code === '0') {
        message.success('发布成功')
      }
    } catch (e) {
      setPublishLoading(false)
    } finally {
      setPublishLoading(false)
    }
  }

  const handleOk = () => {
    form.validateFields().then((values) => {
      saveBasicInfo(values)
    })
  }

  const handleCancel = () => {
    setIsModalOpen(false)
  }

  const onFinish = (values) => {
    console.log('Success:', values)
    saveBasicInfo(values)
  }
  const onFinishFailed = (errorInfo) => {
    console.log('Failed:', errorInfo)
  }

  const onBackClick = () => {
    if (APP_ENV === 'auto') {
      navigate('/')
    } else {
      // console.log('location', location)
      // /sparkos/flow/
      // /sparkos/workspace/flow
      const pathname = window.location.pathname
      console.log('pathname is', pathname)
      window.location.href = `${import.meta.env.VITE_ROUTER_BASE_URL}/workspace/flow/`
    }
  }

  const onExperienceDrawerClose = useCallback(() => {
    setExperienceVisible(false)
  }, [experienceVisible])

  const onTestClick = () => {
    setDebugLoading(true)
    const onResult = (num) => {
      setDebugLoading(false)
      setDebugNumber(num)
      if (num === 0) {
        setExperienceVisible(true)
      }
    }
    eventBus.emit('DEBUG', onResult)
  }

  const onPublishClick = () => {
    setPublishDebugLoading(true)
    const onResult = (num) => {
      setPublishDebugLoading(false)
      setDebugNumber(num)
      if (num <= 0) {
        doPublish()
      }
    }
    eventBus.emit('DEBUG', onResult)
  }

  const onPublishApiModalCancel = () => {
    setPublishApiModalOpen(false)
  }

  const handleMessageFromParent = (event) => {
    // 验证消息来源（生产环境应该检查 event.origin）
    // if (event.origin !== "https://expected-origin.com") return;

    console.log('handleMessageFromParent', event.data)

    const onDebugResult = (num) => {
      setDebugNumber(num)
      window.parent?.postMessage({ type: 'DEBUG_NUMBER', num }, '*')
      if (num === 0) {
        setExperienceVisible(true)
      }
    }

    const onConstructResult = (num) => {
      setDebugNumber(num)
      window.parent?.postMessage({ type: 'DEBUG_NUMBER', num }, '*')
      if (num <= 0) {
        sendToParent({
          type: 'SHOULD_CONSTRUCT_AGENT_FLOW',
          data: {
            id: workflowId,
            flowId: workflowDataRef?.current?.flowId
          }
        })
      }
    }

    const message = event.data

    if (message.type === 'DEBUG_AGENT_FLOW') {
      // parentMessage
      eventBus.emit('DEBUG', onDebugResult)
    } else if (message.type === 'CONSTRUCT_AGENT_FLOW') {
      eventBus.emit('DEBUG', onConstructResult)
    }
  }

  const sendToParent = (message) => {
    // 发送消息到父窗口
    window.parent?.postMessage(message, '*') // 生产环境应该指定具体的 origin 而不是 '*'
  }

  const name = workflowData?.name || ''
  const flowId = workflowData?.flowId
  // 根据name判断请求是否回来
  const workflowSchemaData = name
    ? workflowData?.schema
      ? JSON.parse(workflowData?.schema)
      : {}
    : null
  const initTimestamp = workflowData?.updateTime
    ? dayjs(workflowData.updateTime).format('HH:mm:ss')
    : ''
  const [timestamp, setTimestamp] = useState('')

  const handleMenuClick = (e) => {
    console.log('click', e)
    // e.stopPropagation()
    if (e.key === 'api') {
      setPublishAPIDebugLoading(true)
      const onResult = (num) => {
        // 关闭 dropDown按钮
        setDropDownOpen(false)
        setPublishAPIDebugLoading(false)
        setDebugNumber(num)
        if (num <= 0) {
          setPublishApiModalOpen(true)
        }
      }
      eventBus.emit('DEBUG', onResult)
    }
  }
  const items = [
    {
      label: (
        <Button
          type="text"
          icon={<IconPublish />}
          loading={publishAPIDebugLoading || publishAPILoading}
          ref={dropdownRef}
        >
          {publishAPIDebugLoading ? '校验中...' : publishAPILoading ? '发布中...' : '发布成API'}
        </Button>
      ),
      key: 'api'
    }
  ]

  const menuProps = {
    items,
    onClick: handleMenuClick
  }

  // console.log('now in workflow, workflowSchemaData::', workflowSchemaData)
  //
  return (
    <div className="main">
      <div className="page" ref={editorWrap}>
        {((!inIframe && APP_ENV !== 'aiui') || import.meta.env.DEV) && (
          <div className="page-top">
            <div className="page-top-left">
              <div className="page-title-operate" id="page-title-operate">
                <div
                  className="text-[--flow-desc-color]"
                  style={{ cursor: 'pointer', marginRight: 20 }}
                  onClick={onBackClick}
                >
                  <LeftOutlined />
                </div>

                <div>
                  <div className="flex items-center leading-[24px]">
                    <span title={name} className="mr-1">
                      {name}
                    </span>
                    <FormOutlined className="text-[--flow-desc-color]" onClick={onEditClick} />
                  </div>
                  <p className="page-title-operate-time">
                    {flowId}&nbsp;已自动保存于 {timestamp || initTimestamp}
                  </p>
                </div>
              </div>
            </div>
            <div className="page-top-right">
              <Space size={16}>
                <div className="relative">
                  {debugNumber > 0 && (
                    <div className="w-4 h-4 bg-white border border-solid border-[#f7a50c] rounded-full leading-4 text-center text-[12px] text-[#f7a50c] absolute z-10 top-[-7px] right-[-7px]">
                      {debugNumber}
                    </div>
                  )}
                  <Button onClick={onTestClick} icon={<IconDebug />} loading={debugLoading}>
                    {debugLoading ? '校验中...' : '调试'}
                  </Button>
                </div>

                {APP_ENV === 'auto' ? (
                  <Button
                    type="primary"
                    onClick={onPublishClick}
                    icon={<IconPublish />}
                    loading={publishLoading || publishDebugLoading}
                  >
                    {publishDebugLoading ? '校验中...' : publishLoading ? '发布中...' : '发布'}
                  </Button>
                ) : (
                  <Dropdown menu={menuProps} onClick={onPublishClick} open={dropDownOpen}>
                    <Button
                      type="primary"
                      icon={<IconPublish />}
                      loading={publishLoading || publishDebugLoading}
                      onMouseOver={onPublishEnter}
                    >
                      <Space>
                        {publishDebugLoading ? '校验中...' : publishLoading ? '发布中...' : '发布'}
                        <DownOutlined />
                      </Space>
                    </Button>
                  </Dropdown>
                )}
              </Space>
            </div>
          </div>
        )}
        <div
          className="page-scroll"
          style={{
            height:
              inIframe || (APP_ENV === 'aiui' && !import.meta.env.DEV)
                ? '100%'
                : 'calc(100% - 60px)'
          }}
        >
          <div className="editor">
            {workflowSchemaData ? (
              <Canvas
                key={workflowId}
                flowId={flowId}
                initData={workflowSchemaData}
                experienceVisible={experienceVisible}
                onExperienceDrawerClose={onExperienceDrawerClose}
              />
            ) : (
              <div
                className={styles['editor-canvas']}
                style={{ display: 'flex', alignItems: 'center', justifyContent: 'center' }}
              >
                <Spin
                  indicator={
                    <LoadingOutlined
                      style={{
                        fontSize: 48
                      }}
                      spin
                    />
                  }
                />
              </div>
            )}
            {/* 模型列表为空时，添加画布页面级loading */}
            {maasModels.length === 0 && (
              <div
                className={styles['editor-loading']}
                style={{ display: 'flex', alignItems: 'center', justifyContent: 'center' }}
              >
                <Spin
                  indicator={
                    <LoadingOutlined
                      style={{
                        fontSize: 48
                      }}
                      spin
                    />
                  }
                />
              </div>
            )}
          </div>
        </div>
      </div>
      {workflowData && (
        <Modal
          title="编辑"
          open={isModalOpen}
          onCancel={handleCancel}
          onOk={handleOk}
          confirmLoading={editLoading}
        >
          <Form
            name="basic"
            form={form}
            layout="vertical"
            initialValues={{
              name: workflowData?.name,
              description: workflowData?.description
            }}
            onFinish={onFinish}
            onFinishFailed={onFinishFailed}
            autoComplete="off"
          >
            <Form.Item
              label="名称"
              name="name"
              rules={[
                {
                  required: true,
                  message: '请输入名称'
                },
                {
                  pattern: /^[\u4e00-\u9fffa-zA-Z0-9_]{0,}$/,
                  message: '只支持中文/英文/数字/下划线格式'
                }
              ]}
            >
              <Input placeholder="请输入工作流名称" showCount maxLength={32} />
            </Form.Item>
            <Form.Item
              label="描述"
              name="description"
              rules={[{ required: true, message: '请输入工作流描述' }, { validator: validateTrim }]}
            >
              <Input.TextArea placeholder="请输入工作流描述" showCount maxLength={200} />
            </Form.Item>
          </Form>
        </Modal>
      )}

      <PublishApiModal
        open={publishApiModalOpen}
        onCancel={onPublishApiModalCancel}
        flowId={flowId}
      />
    </div>
  )
}
export default Workflow
