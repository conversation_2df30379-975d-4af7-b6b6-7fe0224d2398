import { StrictMode } from 'react'
import { createRoot } from 'react-dom/client'
import { create<PERSON><PERSON>er<PERSON><PERSON>er, RouterProvider } from 'react-router-dom'

import Login from '@/pages/Login'
import Login2 from '@/pages/Login2'

import Root from '@/pages/Root'
// import App from './App.jsx'
import Pipelines from '@/pages/PipelineList'
import Pipeline from '@/pages/Pipeline'
import zhCN from 'antd/locale/zh_CN'
import { ConfigProvider } from 'antd'
import { APP_ENV } from '@/utils/constant'

import './index.css'
import '@xyflow/react/dist/style.css'
import '@/assets/css/reactflow.scss'
import { cssVariables, token } from '@/utils/constant'

import { message } from 'antd'
import CustomEmpty from '@/components/CustomEmpty'
message.config({
  getContainer: () => document.getElementById('root') || document.body
}) // fix micro-app 没有劫持处理 defaultView的问题
// function ka(e) {
//   return e.ownerDocument.defaultView;
// }
// TODO: civi工作流里 e.ownerDocument.defaultView 没有值？ 是undefined
;(function fixDefaultView() {
  if (!Document.prototype.hasOwnProperty('defaultView')) {
    Object.defineProperty(Document.prototype, 'defaultView', {
      get() {
        // fallback to global window
        return window
      },
      configurable: true
    })
  }
})()

// 如果是子项目，取得cookie信息
if (window.microApp?.getData()) {
  window.__DATA_FROM_PARENT__ = window.microApp?.getData()
  console.log('--------data come from parent-----------', window.window.microApp?.getData())
}

// 设置 CSS 变量的工具函数
const setCSSVariables = (variables) => {
  Object.entries(variables).forEach(([key, value]) => {
    document.documentElement.style.setProperty(key, value)
  })
}

setCSSVariables(cssVariables[APP_ENV])

if (APP_ENV === 'auto') {
  import('@/assets/css/antd_overwrite/index.scss')
    .then(() => {
      console.log('antd_overwrite.scss loaded')
    })
    .catch((err) => {
      console.error('Failed to load antd_overwrite.scss', err)
    })
}

// #409eff

const router = createBrowserRouter(
  [
    {
      path: '/login',
      element: <Login />
    },
    // 开放平台开发登录
    {
      path: '/login2',
      element: <Login2 />
    },
    APP_ENV !== 'aiui' || import.meta.env.DEV
      ? {
          path: '/',
          element: <Root />,
          children: [{ index: true, element: <Pipelines /> }]
        }
      : null,
    {
      path: '/:id',
      element: <Pipeline />
    }
  ].filter(Boolean),
  { basename: `${import.meta.env.VITE_ROUTER_BASE_URL}/flow/` }
)

createRoot(document.getElementById('root')).render(
  // <StrictMode>
  <ConfigProvider
    locale={zhCN}
    theme={{
      cssVar: { key: 'civi', hashed: false },
      token: token[APP_ENV],
      components: {
        Collapse: {
          // contentPadding: '16px 0',
          headerPadding: '10px 0 !important'
        },
        Button: {
          primaryShadow: '',
          colorPrimary: cssVariables[APP_ENV]['--sparkos-button-color'],
          algorithm: true
        }
      }
    }}
    renderEmpty={() => <CustomEmpty />}
  >
    <RouterProvider router={router} />
  </ConfigProvider>
  // </StrictMode>,
)
