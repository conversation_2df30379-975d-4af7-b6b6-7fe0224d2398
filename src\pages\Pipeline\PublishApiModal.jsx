import React, { useEffect, useState } from 'react'
import {
  Space,
  Table,
  Input,
  Button,
  Modal,
  Form,
  Select,
  Tabs,
  message,
  Popconfirm,
  Flex,
  Card,
  Alert,
  Collapse,
  Tag
} from 'antd'

import { CloseCircleOutlined } from '@ant-design/icons'
import IconPublish from 'assets/svgs/publish.svg?react'
import { APP_ENV, cssVariables } from '@/utils/constant'
import IconArrowRight from '@/assets/svgs/arrow-right.svg?react'


import ajax from '@/utils/http'

const { Option } = Select

const PublishApiModal = ({ open, onCancel, flowId }) => {
  const [form] = Form.useForm()

  const [unrelatedApps, setUnrelatedApps] = useState([])
  const [relatedApps, setRelatedApps] = useState([])

  const [publishLoading, setPublishLoading] = useState(false)

  // 下方表格分页参数
  const [tableParams, setTableParams] = useState({
    pagination: {
      current: 1,
      pageSize: 10
    }
  })

  const [tableLoading, setTableLoading] = useState(false)

  useEffect(() => {
    if (open) {
      fetchUnrelatedApps()
      fetchRelatedApps()
    }
  }, [open])

  const fetchUnrelatedApps = () => {
    ajax({
      url: `/workflow/app/list`,
      method: 'get',
      data: {
        flowId: flowId,
        type: 1,
        pageIndex: 1,
        pageSize: 9999
      }
    }).then((res) => {
      console.log('从接口获取的data111', res)
      if (res.data.code === '0') {
        console.log('res---', res)
        setUnrelatedApps(res.data.data?.apps || [])
      }
    })
  }

  const fetchRelatedApps = () => {
    setTableLoading(true)
    // 获取已关联列表
    ajax({
      url: `/workflow/app/list`,
      method: 'get',
      data: {
        flowId: flowId,
        type: 2,
        pageIndex: tableParams.pagination.current,
        pageSize: tableParams.pagination.pageSize
      }
    }).then((res) => {
      if (res.data.code === '0') {
        setTableLoading(false)
        setRelatedApps(res.data.data?.apps || [])
        setTableParams({
          ...tableParams,
          pagination: {
            ...tableParams.pagination,
            total: res.data.data?.count
          }
        })
      }
    })
  }

  useEffect(() => {
    if (open && flowId) {
      fetchRelatedApps()
    }
  }, [tableParams.pagination?.current, tableParams.pagination?.pageSize, open, flowId])

  const handleTableChange = (pagination) => {
    setTableParams({
      pagination
    })

    // `dataSource` is useless since `pageSize` changed
    if (pagination.pageSize !== tableParams.pagination?.pageSize) {
      setRelatedApps([])
    }
  }

  const onFinish = async (values) => {}

  const onFinishFailed = (values) => {
    console.log('onFinishFailed', values)
  }

  const onPublishApiCancel = () => {
    onCancel && onCancel()
  }

  const handleOk = () => {
    form.validateFields().then(async (values) => {
      console.log('-------values------', values)
      setPublishLoading(true)
      try {
        const result = await ajax({
          url: '/workflow/app/publish',
          method: 'post',
          data: JSON.stringify({
            flowId,
            appids: values.appids
          })
        })
        if (result.data.code === '0') {
          message.success('发布成API成功')
          onCancel()
        }
      } catch (e) {
      } finally {
        setPublishLoading(false)
      }
    })
  }

  const columns = [
    {
      title: '应用名称',
      dataIndex: 'appName',
      key: 'appName'
    },
    {
      title: '应用appid',
      dataIndex: 'appid',
      key: 'appid'
    },
    {
      title: '状态',
      dataIndex: 'address',
      render() {
        return <Tag color="success">已发布</Tag>
      }
    }
  ]

  const items = [
    {
      key: '1',
      label: '已关联发布的',
      children: (
        <Table
          dataSource={relatedApps}
          columns={columns}
          pagination={
            (tableParams.pagination.total ?? 0) > tableParams.pagination.pageSize
              ? tableParams.pagination
              : false
          }
          loading={tableLoading}
          onChange={handleTableChange}
        />
      )
    }
  ]

  return (
    <Modal
      title={
        APP_ENV === 'base' ? (
          <Space size={4}>
            <IconPublish /> {'发布成API'}
          </Space>
        ) : (
          '发布成API'
        )
      }
      open={open}
      onOk={handleOk}
      onCancel={onPublishApiCancel}
      destroyOnClose={true}
      width={762}
      okText="发布"
      confirmLoading={publishLoading}
      okButtonProps={
        APP_ENV === 'base'
          ? {
              icon: <IconArrowRight  />,
              iconPosition: 'end'
            }
          : {
              type: 'primary',
              size: 'middle',
              children: '发布'
            }
      }
    >
      <Form
        name="basic"
        form={form}
        labelCol={{
          span: 4
        }}
        wrapperCol={{
          span: 20
        }}
        autoComplete="off"
        preserve={false}
      >
        <Form.Item
          label="关联已有appid"
          name="appids"
          rules={[
            {
              required: true,
              message: '请选择appid'
            }
          ]}
        >
          <Select
            placeholder="选择appid"
            allowClear
            mode="multiple"
            showSearch
            filterOption={(input, option) => {
              console.log('input, option', input, option)
              return (
                (option?.value ?? '').toLowerCase().includes(input.toLowerCase()) ||
                (option?.children?.[0] ?? '').toLowerCase().includes(input.toLowerCase())
              )
            }}
          >
            {unrelatedApps.map((it) => {
              return (
                <Option value={it.appid}>
                  {it.appName}({it.appid})
                </Option>
              )
            })}
          </Select>
        </Form.Item>
      </Form>
      <Alert
        message="温馨提示：将工作流发布成API需要先关联appid进行授权，支持多选。"
        type="info"
        style={{ marginBottom: 10 }}
      />
      <Collapse defaultActiveKey={['1']} ghost items={items} />
    </Modal>
  )
}
export default PublishApiModal
